const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetLessons() {
  console.log('🗑️ Clearing existing lessons...');

  try {
    // Delete all lesson progress first (foreign key constraint)
    await prisma.lessonProgress.deleteMany({});
    console.log('✅ Cleared lesson progress');

    // Delete all lesson exercises first (foreign key constraint)
    await prisma.lessonExercise.deleteMany({});
    console.log('✅ Cleared lesson exercises');

    // Delete all lesson sections
    await prisma.lessonSection.deleteMany({});
    console.log('✅ Cleared lesson sections');

    // Delete all lessons
    await prisma.lesson.deleteMany({});
    console.log('✅ Cleared lessons');

    console.log('🌱 Re-seeding lessons...');

    // Comprehensive lesson data for all CEFR levels
    const lessonsData = [
      // A1 Level Lessons
      {
        title: "French Greetings and Introductions",
        description: "Learn essential French greetings, how to introduce yourself, and basic polite expressions for everyday interactions.",
        level: "A1",
        duration: 15,
        topics: ["greetings", "introductions", "politeness"]
      },
      {
        title: "Numbers and Time",
        description: "Master French numbers from 0-100, tell time, and express dates and ages in French.",
        level: "A1",
        duration: 20,
        topics: ["numbers", "time", "dates"]
      },
      {
        title: "Family and Relationships",
        description: "Vocabulary for family members, describing relationships, and talking about your family in French.",
        level: "A1",
        duration: 18,
        topics: ["family", "relationships", "vocabulary"]
      },
      {
        title: "Colors, Shapes, and Basic Adjectives",
        description: "Learn colors, shapes, and essential adjectives to describe objects and people in French.",
        level: "A1",
        duration: 16,
        topics: ["colors", "adjectives", "descriptions"]
      },
      {
        title: "Food and Drinks",
        description: "Essential vocabulary for food, drinks, ordering at restaurants, and expressing preferences.",
        level: "A1",
        duration: 22,
        topics: ["food", "drinks", "restaurant", "preferences"]
      },
      {
        title: "Shopping and Money",
        description: "Learn to shop in French, ask for prices, handle money, and basic shopping vocabulary.",
        level: "A1",
        duration: 19,
        topics: ["shopping", "money", "prices", "vocabulary"]
      },
      {
        title: "Transportation and Directions",
        description: "Navigate in French: transportation vocabulary, asking for directions, and basic travel phrases.",
        level: "A1",
        duration: 21,
        topics: ["transportation", "directions", "travel"]
      },
      {
        title: "Weather and Seasons",
        description: "Describe weather conditions, seasons, and climate-related vocabulary in French.",
        level: "A1",
        duration: 14,
        topics: ["weather", "seasons", "climate"]
      },

      // A2 Level Lessons
      {
        title: "Past Tense: Passé Composé",
        description: "Master the passé composé tense to talk about completed actions in the past.",
        level: "A2",
        duration: 25,
        topics: ["grammar", "past-tense", "verbs"]
      },
      {
        title: "Daily Routines and Habits",
        description: "Express daily routines, habits, and regular activities using appropriate vocabulary and structures.",
        level: "A2",
        duration: 20,
        topics: ["routines", "habits", "daily-life"]
      },
      {
        title: "Describing People and Personality",
        description: "Advanced vocabulary for physical descriptions and personality traits in French.",
        level: "A2",
        duration: 18,
        topics: ["descriptions", "personality", "people"]
      },
      {
        title: "Health and Body Parts",
        description: "Medical vocabulary, body parts, expressing pain and symptoms, visiting the doctor.",
        level: "A2",
        duration: 23,
        topics: ["health", "body", "medical", "symptoms"]
      },
      {
        title: "Hobbies and Leisure Activities",
        description: "Talk about hobbies, sports, entertainment, and leisure activities in French.",
        level: "A2",
        duration: 19,
        topics: ["hobbies", "sports", "leisure", "entertainment"]
      },
      {
        title: "Making Plans and Invitations",
        description: "Learn to make plans, extend invitations, accept or decline offers politely.",
        level: "A2",
        duration: 17,
        topics: ["plans", "invitations", "social", "politeness"]
      },
      {
        title: "Comparative and Superlative",
        description: "Compare things and people using comparative and superlative forms in French.",
        level: "A2",
        duration: 21,
        topics: ["grammar", "comparisons", "adjectives"]
      },
      {
        title: "Expressing Opinions and Preferences",
        description: "Learn to express opinions, preferences, likes and dislikes with appropriate vocabulary.",
        level: "A2",
        duration: 16,
        topics: ["opinions", "preferences", "expressions"]
      },

      // B1 Level Lessons
      {
        title: "Subjunctive Mood Introduction",
        description: "Introduction to the subjunctive mood: when and how to use it in French.",
        level: "B1",
        duration: 30,
        topics: ["grammar", "subjunctive", "moods"]
      },
      {
        title: "Work and Professional Life",
        description: "Professional vocabulary, job interviews, workplace communication, and career discussions.",
        level: "B1",
        duration: 28,
        topics: ["work", "professional", "career", "interviews"]
      },
      {
        title: "Education and Learning",
        description: "Educational system, academic vocabulary, discussing studies and learning experiences.",
        level: "B1",
        duration: 24,
        topics: ["education", "academic", "studies", "learning"]
      },
      {
        title: "Technology and Modern Life",
        description: "Technology vocabulary, social media, digital communication, and modern lifestyle.",
        level: "B1",
        duration: 22,
        topics: ["technology", "digital", "modern-life", "communication"]
      },
      {
        title: "Environmental Issues",
        description: "Discuss environmental problems, climate change, and sustainability in French.",
        level: "B1",
        duration: 26,
        topics: ["environment", "climate", "sustainability", "ecology"]
      },
      {
        title: "French Culture and Traditions",
        description: "Explore French culture, traditions, holidays, and cultural practices.",
        level: "B1",
        duration: 25,
        topics: ["culture", "traditions", "holidays", "society"]
      },
      {
        title: "Conditional Tense and Hypotheticals",
        description: "Master the conditional tense and express hypothetical situations in French.",
        level: "B1",
        duration: 27,
        topics: ["grammar", "conditional", "hypotheticals"]
      },
      {
        title: "Travel and Tourism",
        description: "Advanced travel vocabulary, planning trips, cultural experiences, and travel stories.",
        level: "B1",
        duration: 23,
        topics: ["travel", "tourism", "culture", "experiences"]
      },

      // B2 Level Lessons
      {
        title: "Complex Grammar Structures",
        description: "Advanced grammar including relative pronouns, complex sentence structures, and stylistic variations.",
        level: "B2",
        duration: 35,
        topics: ["grammar", "complex-structures", "style"]
      },
      {
        title: "French Literature and Arts",
        description: "Explore French literature, analyze texts, discuss artistic movements and cultural heritage.",
        level: "B2",
        duration: 32,
        topics: ["literature", "arts", "culture", "analysis"]
      },
      {
        title: "Politics and Social Issues",
        description: "Discuss political systems, social issues, current events, and civic engagement.",
        level: "B2",
        duration: 30,
        topics: ["politics", "social-issues", "current-events", "society"]
      },
      {
        title: "Business French",
        description: "Professional communication, business vocabulary, negotiations, and formal correspondence.",
        level: "B2",
        duration: 28,
        topics: ["business", "professional", "formal", "communication"]
      },
      {
        title: "Media and Communication",
        description: "Analyze media content, discuss journalism, advertising, and communication strategies.",
        level: "B2",
        duration: 26,
        topics: ["media", "journalism", "communication", "analysis"]
      },
      {
        title: "Philosophy and Abstract Concepts",
        description: "Discuss philosophical concepts, abstract ideas, and complex reasoning in French.",
        level: "B2",
        duration: 33,
        topics: ["philosophy", "abstract", "reasoning", "concepts"]
      },
      {
        title: "Scientific and Technical French",
        description: "Scientific vocabulary, technical explanations, and academic discourse.",
        level: "B2",
        duration: 29,
        topics: ["science", "technical", "academic", "vocabulary"]
      },
      {
        title: "Regional Variations and Francophonie",
        description: "Explore French variations across different regions and francophone countries.",
        level: "B2",
        duration: 24,
        topics: ["regional", "francophonie", "variations", "culture"]
      }
    ];

    // Add lessons in batches
    const batchSize = 10;
    for (let i = 0; i < lessonsData.length; i += batchSize) {
      const batch = lessonsData.slice(i, i + batchSize);
      await prisma.lesson.createMany({
        data: batch,
        skipDuplicates: true,
      });
      console.log(`✅ Added batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(lessonsData.length / batchSize)}`);
    }

    console.log(`🎉 Successfully reset and seeded ${lessonsData.length} lessons!`);

  } catch (error) {
    console.error('❌ Error resetting lessons:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetLessons();
