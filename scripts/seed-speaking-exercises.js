const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedSpeakingExercises() {
  console.log('🗣️ Seeding speaking exercises...');

  try {
    // Check if pronunciation exercises already exist (we'll use these for speaking)
    const existingCount = await prisma.pronunciationExercise.count();
    if (existingCount > 60) { // We already have 60 basic ones, add more advanced ones
      console.log(`Found ${existingCount} existing pronunciation exercises, adding more advanced ones...`);
    }

    const speakingExercises = [
      // Beginner Level
      {
        text: 'Comment allez-vous aujourd\'hui?',
        translation: 'How are you today?',
        difficulty: 'A1',
        category: 'greetings',
        expectedPronunciation: 'kɔ.mɑ̃ t‿a.le vu o.ʒuʁ.dɥi'
      },
      {
        text: 'Je m\'appelle Marie. Et vous?',
        translation: 'My name is <PERSON>. And you?',
        difficulty: 'A1',
        category: 'greetings',
        expectedPronunciation: 'ʒə ma.pɛl ma.ʁi. e vu'
      },
      {
        text: 'Où est la boulangerie?',
        translation: 'Where is the bakery?',
        difficulty: 'A1',
        category: 'travel',
        expectedPronunciation: 'u ɛ la bu.lɑ̃.ʒə.ʁi'
      },
      {
        text: 'Je voudrais un café, s\'il vous plaît.',
        translation: 'I would like a coffee, please.',
        difficulty: 'A1',
        category: 'dining',
        expectedPronunciation: 'ʒə vu.dʁɛ œ̃ ka.fe sil vu plɛ'
      },
      {
        text: 'Quelle heure est-il?',
        translation: 'What time is it?',
        difficulty: 'A1',
        category: 'everyday',
        expectedPronunciation: 'kɛl œʁ ɛ.t‿il'
      },
      {
        text: 'Combien ça coûte?',
        translation: 'How much does it cost?',
        difficulty: 'A1',
        category: 'shopping',
        expectedPronunciation: 'kɔ̃.bjɛ̃ sa kut'
      },
      {
        text: 'Merci beaucoup pour votre aide.',
        translation: 'Thank you very much for your help.',
        difficulty: 'A1',
        category: 'greetings',
        expectedPronunciation: 'mɛʁ.si bo.ku puʁ vɔ.tʁə ɛd'
      },
      {
        text: 'Excusez-moi, où sont les toilettes?',
        translation: 'Excuse me, where are the restrooms?',
        difficulty: 'A1',
        category: 'travel',
        expectedPronunciation: 'ɛk.sky.ze mwa u sɔ̃ le twa.lɛt'
      },

      // A2 Level
      {
        text: 'Pourriez-vous me recommander un bon restaurant?',
        translation: 'Could you recommend a good restaurant to me?',
        difficulty: 'A2',
        category: 'dining',
        expectedPronunciation: 'pu.ʁje vu mə ʁə.kɔ.mɑ̃.de œ̃ bɔ̃ ʁɛs.to.ʁɑ̃'
      },
      {
        text: 'Je cherche un hôtel pas trop cher pour trois nuits.',
        translation: 'I\'m looking for a reasonably priced hotel for three nights.',
        difficulty: 'A2',
        category: 'travel',
        expectedPronunciation: 'ʒə ʃɛʁʃ œ̃ o.tɛl pa tʁo ʃɛʁ puʁ tʁwa nɥi'
      },
      {
        text: 'J\'ai réservé une table pour deux personnes à 20h.',
        translation: 'I\'ve reserved a table for two people at 8 PM.',
        difficulty: 'A2',
        category: 'dining',
        expectedPronunciation: 'ʒe ʁe.zɛʁ.ve yn tabl puʁ dø pɛʁ.sɔn a vɛ̃ œʁ'
      },
      {
        text: 'Quels sont vos horaires d\'ouverture?',
        translation: 'What are your opening hours?',
        difficulty: 'A2',
        category: 'everyday',
        expectedPronunciation: 'kɛl sɔ̃ vo o.ʁɛʁ du.vɛʁ.tyʁ'
      },
      {
        text: 'Je voudrais essayer cette veste en taille moyenne.',
        translation: 'I would like to try this jacket in medium size.',
        difficulty: 'A2',
        category: 'shopping',
        expectedPronunciation: 'ʒə vu.dʁɛ ɛ.se.je sɛt vɛst ɑ̃ taj mwa.jɛn'
      },
      {
        text: 'Pouvez-vous m\'aider à trouver la gare?',
        translation: 'Can you help me find the train station?',
        difficulty: 'A2',
        category: 'travel',
        expectedPronunciation: 'pu.ve vu mɛ.de a tʁu.ve la gaʁ'
      },

      // B1 Level
      {
        text: 'Je ne suis pas d\'accord avec votre analyse de la situation.',
        translation: 'I don\'t agree with your analysis of the situation.',
        difficulty: 'B1',
        category: 'business',
        expectedPronunciation: 'ʒə nə sɥi pa da.kɔʁ a.vɛk vɔ.tʁə a.na.liz də la si.tɥa.sjɔ̃'
      },
      {
        text: 'Nous devrions envisager d\'autres options avant de décider.',
        translation: 'We should consider other options before deciding.',
        difficulty: 'B1',
        category: 'business',
        expectedPronunciation: 'nu də.vʁjɔ̃ ɑ̃.vi.za.ʒe do.tʁə ɔp.sjɔ̃ a.vɑ̃ də de.si.de'
      },
      {
        text: 'Pourriez-vous m\'expliquer les implications de cette politique?',
        translation: 'Could you explain the implications of this policy to me?',
        difficulty: 'B1',
        category: 'business',
        expectedPronunciation: 'pu.ʁje vu mɛks.pli.ke le ɛ̃.pli.ka.sjɔ̃ də sɛt po.li.tik'
      },
      {
        text: 'J\'ai eu un problème avec ma réservation.',
        translation: 'I had a problem with my reservation.',
        difficulty: 'B1',
        category: 'travel',
        expectedPronunciation: 'ʒe y œ̃ pʁo.blɛm a.vɛk ma ʁe.zɛʁ.va.sjɔ̃'
      },
      {
        text: 'Les transports en commun sont-ils fiables dans cette ville?',
        translation: 'Is public transportation reliable in this city?',
        difficulty: 'B1',
        category: 'travel',
        expectedPronunciation: 'le tʁɑ̃s.pɔʁ ɑ̃ kɔ.mœ̃ sɔ̃.t‿il fi.jabl dɑ̃ sɛt vil'
      },

      // B2 Level
      {
        text: 'Je cherche un cadeau pour quelqu\'un qui s\'intéresse à l\'art contemporain.',
        translation: 'I\'m looking for a gift for someone who is interested in contemporary art.',
        difficulty: 'B2',
        category: 'shopping',
        expectedPronunciation: 'ʒə ʃɛʁʃ œ̃ ka.do puʁ kɛl.kœ̃ ki sɛ̃.te.ʁɛs a laʁ kɔ̃.tɑ̃.po.ʁɛ̃'
      },
      {
        text: 'Cette exposition présente une perspective unique sur l\'histoire française.',
        translation: 'This exhibition presents a unique perspective on French history.',
        difficulty: 'B2',
        category: 'culture',
        expectedPronunciation: 'sɛt ɛks.po.zi.sjɔ̃ pʁe.zɑ̃t yn pɛʁs.pɛk.tiv y.nik syʁ lis.twaʁ fʁɑ̃.sɛz'
      },
      {
        text: 'Il faut que nous discutions des détails du contrat avant de signer.',
        translation: 'We need to discuss the contract details before signing.',
        difficulty: 'B2',
        category: 'business',
        expectedPronunciation: 'il fo kə nu dis.ky.tjɔ̃ de de.taj dy kɔ̃.tʁa a.vɑ̃ də si.ɲe'
      },

      // C1 Level
      {
        text: 'Cette décision aura des répercussions considérables sur l\'économie nationale.',
        translation: 'This decision will have considerable repercussions on the national economy.',
        difficulty: 'C1',
        category: 'politics',
        expectedPronunciation: 'sɛt de.si.zjɔ̃ o.ʁa de ʁe.pɛʁ.ky.sjɔ̃ kɔ̃.si.de.ʁabl syʁ le.ko.no.mi na.sjo.nal'
      },
      {
        text: 'L\'analyse approfondie de ces données révèle des tendances inquiétantes.',
        translation: 'The in-depth analysis of this data reveals worrying trends.',
        difficulty: 'C1',
        category: 'academic',
        expectedPronunciation: 'la.na.liz a.pʁo.fɔ̃.di də se do.ne ʁe.vɛl de tɑ̃.dɑ̃s ɛ̃.kje.tɑ̃t'
      }
    ];

    // Add exercises in batches
    const batchSize = 10;
    for (let i = 0; i < speakingExercises.length; i += batchSize) {
      const batch = speakingExercises.slice(i, i + batchSize);
      await prisma.pronunciationExercise.createMany({
        data: batch,
        skipDuplicates: true,
      });
      console.log(`✅ Added batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(speakingExercises.length / batchSize)}`);
    }

    console.log(`🎉 Successfully seeded ${speakingExercises.length} speaking exercises!`);

  } catch (error) {
    console.error('❌ Error seeding speaking exercises:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedSpeakingExercises();
